package com.bj58.hy.wx.qywxbiz.interfaces.web;

import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 监控页面控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/monitor/view")
public class MonitorViewController {

    @Autowired
    private MonitorDataService monitorDataService;

    /**
     * 监控首页
     */
    @GetMapping("/")
    public String index() {
        return "monitor/index";
    }

    /**
     * 监控概览页面
     */
    @GetMapping("/overview")
    public String overview(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {
        
        try {
            Map<String, Object> overviewData = monitorDataService.getMonitorOverview(corpId, days);
            
            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("overviewData", overviewData);
            model.addAttribute("pageTitle", "监控概览");

            return "monitor/overview";
        } catch (Exception e) {
            log.error("获取监控概览数据失败", e);
            model.addAttribute("error", "获取监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * 欢迎语监控页面
     */
    @GetMapping("/welcome")
    public String welcome(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "default") String bizScene,
            @RequestParam(defaultValue = "3") int days,
            Model model) {
        
        try {
            Map<String, Object> welcomeData = monitorDataService.getWelcomeMonitorData(corpId, bizScene, days);
            
            model.addAttribute("corpId", corpId);
            model.addAttribute("bizScene", bizScene);
            model.addAttribute("days", days);
            model.addAttribute("welcomeData", welcomeData);
            model.addAttribute("pageTitle", "欢迎语监控");

            return "monitor/welcome";
        } catch (Exception e) {
            log.error("获取欢迎语监控数据失败", e);
            model.addAttribute("error", "获取欢迎语监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * AI聊天监控页面
     */
    @GetMapping("/ai-chat")
    public String aiChat(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {
        
        try {
            Map<String, Object> aiChatData = monitorDataService.getAiChatMonitorData(corpId, days);
            
            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("aiChatData", aiChatData);
            model.addAttribute("pageTitle", "AI聊天监控");

            return "monitor/ai-chat";
        } catch (Exception e) {
            log.error("获取AI聊天监控数据失败", e);
            model.addAttribute("error", "获取AI聊天监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * 消息处理监控页面
     */
    @GetMapping("/message-process")
    public String messageProcess(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {
        
        try {
            Map<String, Object> messageProcessData = monitorDataService.getMessageProcessMonitorData(corpId, days);
            
            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("messageProcessData", messageProcessData);
            model.addAttribute("pageTitle", "消息处理监控");

            return "monitor/message-process";
        } catch (Exception e) {
            log.error("获取消息处理监控数据失败", e);
            model.addAttribute("error", "获取消息处理监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * 用户满意度监控页面
     */
    @GetMapping("/user-satisfaction")
    public String userSatisfaction(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {
        
        try {
            Map<String, Object> userSatisfactionData = monitorDataService.getUserSatisfactionMonitorData(corpId, days);
            
            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("userSatisfactionData", userSatisfactionData);
            model.addAttribute("pageTitle", "用户满意度监控");

            return "monitor/user-satisfaction";
        } catch (Exception e) {
            log.error("获取用户满意度监控数据失败", e);
            model.addAttribute("error", "获取用户满意度监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * 外部服务监控页面
     */
    @GetMapping("/external-service")
    public String externalService(
            @RequestParam(defaultValue = "") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {

        try {
            Map<String, Object> externalServiceData = monitorDataService.getExternalServiceMonitorData(corpId, days);

            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("externalServiceData", externalServiceData);
            model.addAttribute("pageTitle", "外部服务监控");

            return "monitor/external-service";
        } catch (Exception e) {
            log.error("获取外部服务监控数据失败", e);
            model.addAttribute("error", "获取外部服务监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }

    /**
     * 数据生成器页面
     */
    @GetMapping("/data-generator")
    public String dataGenerator() {
        return "monitor/data-generator";
    }
}

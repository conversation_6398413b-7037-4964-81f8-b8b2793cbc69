package com.bj58.hy.wx.qywxbiz.interfaces.web;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorDataGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 监控数据生成控制器 - 用于生成测试数据
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/monitor/data")
public class MonitorDataController {

    @Autowired
    private MonitorDataGenerator monitorDataGenerator;

    /**
     * 生成所有监控数据
     */
    @PostMapping("/generate")
    public Result<String> generateAllData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成监控数据，天数: {}", days);
            monitorDataGenerator.generateAllMonitorData(days);
            return Result.success("监控数据生成成功，生成了" + days + "天的数据");
        } catch (Exception e) {
            log.error("生成监控数据失败", e);
            return Result.failure("生成监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成欢迎语监控数据
     */
    @PostMapping("/generate/welcome")
    public Result<String> generateWelcomeData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成欢迎语监控数据，天数: {}", days);
            monitorDataGenerator.generateWelcomeData(days);
            return Result.success("欢迎语监控数据生成成功");
        } catch (Exception e) {
            log.error("生成欢迎语监控数据失败", e);
            return Result.failure("生成欢迎语监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI聊天监控数据
     */
    @PostMapping("/generate/ai-chat")
    public Result<String> generateAiChatData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成AI聊天监控数据，天数: {}", days);
            monitorDataGenerator.generateAiChatData(days);
            return Result.success("AI聊天监控数据生成成功");
        } catch (Exception e) {
            log.error("生成AI聊天监控数据失败", e);
            return Result.failure("生成AI聊天监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成消息处理监控数据
     */
    @PostMapping("/generate/message-process")
    public Result<String> generateMessageProcessData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成消息处理监控数据，天数: {}", days);
            monitorDataGenerator.generateMessageProcessData(days);
            return Result.success("消息处理监控数据生成成功");
        } catch (Exception e) {
            log.error("生成消息处理监控数据失败", e);
            return Result.failure("生成消息处理监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成用户满意度监控数据
     */
    @PostMapping("/generate/user-satisfaction")
    public Result<String> generateUserSatisfactionData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成用户满意度监控数据，天数: {}", days);
            monitorDataGenerator.generateUserSatisfactionData(days);
            return Result.success("用户满意度监控数据生成成功");
        } catch (Exception e) {
            log.error("生成用户满意度监控数据失败", e);
            return Result.failure("生成用户满意度监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成外部服务监控数据
     */
    @PostMapping("/generate/external-service")
    public Result<String> generateExternalServiceData(@RequestParam(defaultValue = "7") int days) {
        try {
            log.info("开始生成外部服务监控数据，天数: {}", days);
            monitorDataGenerator.generateExternalServiceData(days);
            return Result.success("外部服务监控数据生成成功");
        } catch (Exception e) {
            log.error("生成外部服务监控数据失败", e);
            return Result.failure("生成外部服务监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 清空所有监控数据
     */
    @DeleteMapping("/clear")
    public Result<String> clearAllData() {
        try {
            log.info("开始清空监控数据");
            monitorDataGenerator.clearAllMonitorData();
            return Result.success("监控数据清空成功");
        } catch (Exception e) {
            log.error("清空监控数据失败", e);
            return Result.failure("清空监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试Redis连接
     */
    @GetMapping("/test-redis")
    public Result<String> testRedis() {
        try {
            log.info("测试Redis连接");
            monitorDataGenerator.testRedisConnection();
            return Result.success("Redis连接正常");
        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            return Result.failure("Redis连接失败: " + e.getMessage());
        }
    }

    /**
     * 生成简单测试数据
     */
    @PostMapping("/generate-simple")
    public Result<String> generateSimpleData() {
        try {
            log.info("开始生成简单测试数据");
            monitorDataGenerator.generateSimpleTestData();
            return Result.success("简单测试数据生成成功");
        } catch (Exception e) {
            log.error("生成简单测试数据失败", e);
            return Result.failure("生成简单测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 查看Redis中的数据
     */
    @GetMapping("/check-data")
    public Result<String> checkData() {
        try {
            log.info("检查Redis中的数据");
            String result = monitorDataGenerator.checkRedisData();
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查Redis数据失败", e);
            return Result.failure("检查Redis数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试JSON返回格式
     */
    @GetMapping("/test-json")
    public Result<String> testJson() {
        try {
            log.info("测试JSON返回格式");
            return Result.success("测试成功");
        } catch (Exception e) {
            log.error("测试失败", e);
            return Result.failure("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试AI聊天监控数据查询
     */
    @GetMapping("/test-ai-data")
    public Result<String> testAiData() {
        try {
            log.info("测试AI聊天监控数据查询");
            String result = monitorDataGenerator.testAiDataQuery();
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试AI数据查询失败", e);
            return Result.failure("测试AI数据查询失败: " + e.getMessage());
        }
    }
}

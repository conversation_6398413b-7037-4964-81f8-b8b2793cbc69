package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 监控数据生成器 - 用于生成测试数据
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonitorDataGenerator {

    @Autowired
    private RedissonClient redisson;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String CORP_ID = "ww5cfa32107e9a1f20";
    private static final Random random = new Random();

    /**
     * 生成所有监控数据
     */
    public void generateAllMonitorData(int days) {
        log.info("开始生成监控数据，天数: {}", days);
        
        generateWelcomeData(days);
        generateAiChatData(days);
        generateMessageProcessData(days);
        generateUserSatisfactionData(days);
        generateExternalServiceData(days);
        
        log.info("监控数据生成完成");
    }

    /**
     * 生成欢迎语监控数据
     */
    public void generateWelcomeData(int days) {
        log.info("生成欢迎语监控数据");

        // 修复日期计算，与MonitorDataService保持一致
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            String date = today.minusDays(i).format(DATE_FORMATTER);
            
            // 欢迎语生成成功/失败数据
            setCounterValue("welcome:generate:success:" + CORP_ID + ":default", date, 800 + random.nextInt(200));
            setCounterValue("welcome:generate:failure:" + CORP_ID + ":default", date, 10 + random.nextInt(20));
            
            // 缓存命中/未命中数据
            setCounterValue("welcome:cache:hit", date, 700 + random.nextInt(150));
            setCounterValue("welcome:cache:miss", date, 50 + random.nextInt(50));
        }
        
        // 重试次数详情
        RList<String> retryList = redisson.getList("welcome:retry:count", StringCodec.INSTANCE);
        retryList.clear();
        for (int i = 0; i < 50; i++) {
            retryList.add("重试次数: " + (1 + random.nextInt(3)) + ", 时间: " + System.currentTimeMillis());
        }
        
        // 发送耗时详情
        RList<String> durationList = redisson.getList("welcome:send:duration", StringCodec.INSTANCE);
        durationList.clear();
        for (int i = 0; i < 50; i++) {
            durationList.add("耗时: " + (1000 + random.nextInt(5000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成AI聊天监控数据
     */
    public void generateAiChatData(int days) {
        log.info("生成AI聊天监控数据");

        // 修复日期计算，与MonitorDataService保持一致
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            String date = today.minusDays(i).format(DATE_FORMATTER);
            
            // 同步调用成功/失败数据
            setCounterValue("ai:call:success:" + CORP_ID + ":sync", date, 600 + random.nextInt(200));
            setCounterValue("ai:call:failure:" + CORP_ID + ":sync", date, 20 + random.nextInt(30));
            
            // 异步调用成功/失败数据
            setCounterValue("ai:call:success:" + CORP_ID + ":async", date, 400 + random.nextInt(150));
            setCounterValue("ai:call:failure:" + CORP_ID + ":async", date, 15 + random.nextInt(25));
            
            // AI结果类型分布
            setCounterValue("ai:result:type:" + CORP_ID + ":0", date, 500 + random.nextInt(200)); // 普通消息
            setCounterValue("ai:result:type:" + CORP_ID + ":1", date, 80 + random.nextInt(40));  // 转人工
            setCounterValue("ai:result:type:" + CORP_ID + ":3", date, 20 + random.nextInt(20));  // 消息丢弃
            setCounterValue("ai:result:type:" + CORP_ID + ":5", date, 30 + random.nextInt(20));  // 用户要求转人工
            
            // 承接成功/失败数据
            setCounterValue("ai:takeover:success:" + CORP_ID, date, 450 + random.nextInt(100));
            setCounterValue("ai:takeover:failure:" + CORP_ID, date, 50 + random.nextInt(30));
        }
        
        // 响应时间详情
        RList<String> responseTimeList = redisson.getList("ai:response:time", StringCodec.INSTANCE);
        responseTimeList.clear();
        for (int i = 0; i < 50; i++) {
            responseTimeList.add("响应时间: " + (2000 + random.nextInt(8000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成消息处理监控数据
     */
    public void generateMessageProcessData(int days) {
        log.info("生成消息处理监控数据");

        // 修复日期计算，与MonitorDataService保持一致
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            String date = today.minusDays(i).format(DATE_FORMATTER);
            
            // 首次咨询承接成功/失败数据
            setCounterValue("first:consult:success:" + CORP_ID, date, 300 + random.nextInt(100));
            setCounterValue("first:consult:failure:" + CORP_ID, date, 30 + random.nextInt(20));
            
            // 人工介入数据
            setCounterValue("human:intervention:" + CORP_ID, date, 80 + random.nextInt(40));
        }
        
        // 处理延迟详情
        RList<String> latencyList = redisson.getList("message:latency", StringCodec.INSTANCE);
        latencyList.clear();
        for (int i = 0; i < 50; i++) {
            latencyList.add("处理延迟: " + (3000 + random.nextInt(10000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成用户满意度监控数据
     */
    public void generateUserSatisfactionData(int days) {
        log.info("生成用户满意度监控数据");

        // 修复日期计算，与MonitorDataService保持一致
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            String date = today.minusDays(i).format(DATE_FORMATTER);
            
            // 满意度调查发送数据
            setCounterValue("satisfaction:survey:sent:" + CORP_ID, date, 200 + random.nextInt(100));
            
            // 满意度反馈数据
            setCounterValue("satisfaction:positive:" + CORP_ID, date, 120 + random.nextInt(60));
            setCounterValue("satisfaction:negative:" + CORP_ID, date, 30 + random.nextInt(20));
            
            // 反馈回收数据
            setCounterValue("feedback:recovery:sent:" + CORP_ID, date, 50 + random.nextInt(30));
        }
    }

    /**
     * 生成外部服务监控数据
     */
    public void generateExternalServiceData(int days) {
        log.info("生成外部服务监控数据");

        // 修复日期计算，与MonitorDataService保持一致
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            String date = today.minusDays(i).format(DATE_FORMATTER);
            
            // Dify API调用成功/失败数据
            setCounterValue("dify:api:success:" + CORP_ID, date, 500 + random.nextInt(200));
            setCounterValue("dify:api:failure:" + CORP_ID, date, 15 + random.nextInt(25));
            
            // 订单查询成功/失败数据
            setCounterValue("order:query:success:" + CORP_ID, date, 300 + random.nextInt(150));
            setCounterValue("order:query:failure:" + CORP_ID, date, 5 + random.nextInt(15));
        }
    }

    /**
     * 设置计数器值
     */
    private void setCounterValue(String key, String date, long value) {
        try {
            String fullKey = key + ":" + date;
            RAtomicLong atomicLong = redisson.getAtomicLong(fullKey);
            atomicLong.set(value);
            log.info("设置计数器成功: {} = {}", fullKey, value);
        } catch (Exception e) {
            log.error("设置计数器失败: key={}, date={}, value={}", key, date, value, e);
            throw new RuntimeException("设置计数器失败", e);
        }
    }

    /**
     * 清空所有监控数据
     */
    public void clearAllMonitorData() {
        log.info("清空所有监控数据");

        // 这里可以添加清空逻辑，但要小心不要误删其他数据
        // 建议只在测试环境使用

        log.info("监控数据清空完成");
    }

    /**
     * 测试Redis连接
     */
    public void testRedisConnection() {
        log.info("测试Redis连接");

        try {
            // 测试写入
            String testKey = "monitor:test:connection";
            RAtomicLong testCounter = redisson.getAtomicLong(testKey);
            testCounter.set(123);

            // 测试读取
            long value = testCounter.get();
            log.info("Redis测试成功，写入和读取值: {}", value);

            // 清理测试数据
            testCounter.delete();

        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            throw e;
        }
    }

    /**
     * 生成简单测试数据
     */
    public void generateSimpleTestData() {
        log.info("生成简单测试数据");

        try {
            String today = LocalDate.now().format(DATE_FORMATTER);

            // 生成欢迎语数据
            setCounterValue("welcome:generate:success:" + CORP_ID + ":default", today, 100);
            setCounterValue("welcome:generate:failure:" + CORP_ID + ":default", today, 5);

            // 生成AI聊天数据
            setCounterValue("ai:call:success:" + CORP_ID + ":sync", today, 200);
            setCounterValue("ai:call:failure:" + CORP_ID + ":sync", today, 10);

            log.info("简单测试数据生成完成");

        } catch (Exception e) {
            log.error("生成简单测试数据失败", e);
            throw e;
        }
    }

    /**
     * 检查Redis中的数据
     */
    public String checkRedisData() {
        log.info("检查Redis中的数据");

        try {
            String today = LocalDate.now().format(DATE_FORMATTER);
            StringBuilder result = new StringBuilder();

            result.append("=== 检查Redis数据 ===\n");
            result.append("企业ID: ").append(CORP_ID).append("\n");
            result.append("当前日期: ").append(today).append("\n\n");

            // 检查欢迎语数据
            String welcomeSuccessKey = "welcome:generate:success:" + CORP_ID + ":default:" + today;
            String welcomeFailureKey = "welcome:generate:failure:" + CORP_ID + ":default:" + today;

            RAtomicLong welcomeSuccess = redisson.getAtomicLong(welcomeSuccessKey);
            RAtomicLong welcomeFailure = redisson.getAtomicLong(welcomeFailureKey);

            result.append("=== 欢迎语数据 ===\n");
            result.append("成功key: ").append(welcomeSuccessKey).append(" = ").append(welcomeSuccess.get()).append("\n");
            result.append("失败key: ").append(welcomeFailureKey).append(" = ").append(welcomeFailure.get()).append("\n\n");

            // 检查AI聊天数据
            String aiSyncSuccessKey = "ai:call:success:" + CORP_ID + ":sync:" + today;
            String aiSyncFailureKey = "ai:call:failure:" + CORP_ID + ":sync:" + today;
            String aiAsyncSuccessKey = "ai:call:success:" + CORP_ID + ":async:" + today;
            String aiAsyncFailureKey = "ai:call:failure:" + CORP_ID + ":async:" + today;

            RAtomicLong aiSyncSuccess = redisson.getAtomicLong(aiSyncSuccessKey);
            RAtomicLong aiSyncFailure = redisson.getAtomicLong(aiSyncFailureKey);
            RAtomicLong aiAsyncSuccess = redisson.getAtomicLong(aiAsyncSuccessKey);
            RAtomicLong aiAsyncFailure = redisson.getAtomicLong(aiAsyncFailureKey);

            result.append("=== AI聊天数据 ===\n");
            result.append("同步成功key: ").append(aiSyncSuccessKey).append(" = ").append(aiSyncSuccess.get()).append("\n");
            result.append("同步失败key: ").append(aiSyncFailureKey).append(" = ").append(aiSyncFailure.get()).append("\n");
            result.append("异步成功key: ").append(aiAsyncSuccessKey).append(" = ").append(aiAsyncSuccess.get()).append("\n");
            result.append("异步失败key: ").append(aiAsyncFailureKey).append(" = ").append(aiAsyncFailure.get()).append("\n\n");

            // 检查AI结果类型数据
            result.append("=== AI结果类型数据 ===\n");
            for (int type = 0; type <= 5; type++) {
                String typeKey = "ai:result:type:" + CORP_ID + ":" + type + ":" + today;
                RAtomicLong typeValue = redisson.getAtomicLong(typeKey);
                result.append("类型").append(type).append("key: ").append(typeKey).append(" = ").append(typeValue.get()).append("\n");
            }
            result.append("\n");

            // 检查承接数据
            String takeoverSuccessKey = "ai:takeover:success:" + CORP_ID + ":" + today;
            String takeoverFailureKey = "ai:takeover:failure:" + CORP_ID + ":" + today;

            RAtomicLong takeoverSuccess = redisson.getAtomicLong(takeoverSuccessKey);
            RAtomicLong takeoverFailure = redisson.getAtomicLong(takeoverFailureKey);

            result.append("=== AI承接数据 ===\n");
            result.append("承接成功key: ").append(takeoverSuccessKey).append(" = ").append(takeoverSuccess.get()).append("\n");
            result.append("承接失败key: ").append(takeoverFailureKey).append(" = ").append(takeoverFailure.get()).append("\n");

            log.info("Redis数据检查结果: {}", result.toString());
            return result.toString();

        } catch (Exception e) {
            log.error("检查Redis数据失败", e);
            throw e;
        }
    }
}

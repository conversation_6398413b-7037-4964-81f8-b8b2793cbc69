package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 监控数据生成器 - 用于生成测试数据
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonitorDataGenerator {

    @Autowired
    private RedissonClient redisson;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String CORP_ID = "ww5cfa32107e9a1f20";
    private static final Random random = new Random();

    /**
     * 生成所有监控数据
     */
    public void generateAllMonitorData(int days) {
        log.info("开始生成监控数据，天数: {}", days);
        
        generateWelcomeData(days);
        generateAiChatData(days);
        generateMessageProcessData(days);
        generateUserSatisfactionData(days);
        generateExternalServiceData(days);
        
        log.info("监控数据生成完成");
    }

    /**
     * 生成欢迎语监控数据
     */
    public void generateWelcomeData(int days) {
        log.info("生成欢迎语监控数据");
        
        for (int i = 0; i < days; i++) {
            String date = LocalDate.now().minusDays(i).format(DATE_FORMATTER);
            
            // 欢迎语生成成功/失败数据
            setCounterValue("welcome:generate:success:" + CORP_ID + ":default", date, 800 + random.nextInt(200));
            setCounterValue("welcome:generate:failure:" + CORP_ID + ":default", date, 10 + random.nextInt(20));
            
            // 缓存命中/未命中数据
            setCounterValue("welcome:cache:hit", date, 700 + random.nextInt(150));
            setCounterValue("welcome:cache:miss", date, 50 + random.nextInt(50));
        }
        
        // 重试次数详情
        RList<String> retryList = redisson.getList("welcome:retry:count", StringCodec.INSTANCE);
        retryList.clear();
        for (int i = 0; i < 50; i++) {
            retryList.add("重试次数: " + (1 + random.nextInt(3)) + ", 时间: " + System.currentTimeMillis());
        }
        
        // 发送耗时详情
        RList<String> durationList = redisson.getList("welcome:send:duration", StringCodec.INSTANCE);
        durationList.clear();
        for (int i = 0; i < 50; i++) {
            durationList.add("耗时: " + (1000 + random.nextInt(5000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成AI聊天监控数据
     */
    public void generateAiChatData(int days) {
        log.info("生成AI聊天监控数据");
        
        for (int i = 0; i < days; i++) {
            String date = LocalDate.now().minusDays(i).format(DATE_FORMATTER);
            
            // 同步调用成功/失败数据
            setCounterValue("ai:call:success:" + CORP_ID + ":sync", date, 600 + random.nextInt(200));
            setCounterValue("ai:call:failure:" + CORP_ID + ":sync", date, 20 + random.nextInt(30));
            
            // 异步调用成功/失败数据
            setCounterValue("ai:call:success:" + CORP_ID + ":async", date, 400 + random.nextInt(150));
            setCounterValue("ai:call:failure:" + CORP_ID + ":async", date, 15 + random.nextInt(25));
            
            // AI结果类型分布
            setCounterValue("ai:result:type:" + CORP_ID + ":0", date, 500 + random.nextInt(200)); // 普通消息
            setCounterValue("ai:result:type:" + CORP_ID + ":1", date, 80 + random.nextInt(40));  // 转人工
            setCounterValue("ai:result:type:" + CORP_ID + ":3", date, 20 + random.nextInt(20));  // 消息丢弃
            setCounterValue("ai:result:type:" + CORP_ID + ":5", date, 30 + random.nextInt(20));  // 用户要求转人工
            
            // 承接成功/失败数据
            setCounterValue("ai:takeover:success:" + CORP_ID, date, 450 + random.nextInt(100));
            setCounterValue("ai:takeover:failure:" + CORP_ID, date, 50 + random.nextInt(30));
        }
        
        // 响应时间详情
        RList<String> responseTimeList = redisson.getList("ai:response:time", StringCodec.INSTANCE);
        responseTimeList.clear();
        for (int i = 0; i < 50; i++) {
            responseTimeList.add("响应时间: " + (2000 + random.nextInt(8000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成消息处理监控数据
     */
    public void generateMessageProcessData(int days) {
        log.info("生成消息处理监控数据");
        
        for (int i = 0; i < days; i++) {
            String date = LocalDate.now().minusDays(i).format(DATE_FORMATTER);
            
            // 首次咨询承接成功/失败数据
            setCounterValue("first:consult:success:" + CORP_ID, date, 300 + random.nextInt(100));
            setCounterValue("first:consult:failure:" + CORP_ID, date, 30 + random.nextInt(20));
            
            // 人工介入数据
            setCounterValue("human:intervention:" + CORP_ID, date, 80 + random.nextInt(40));
        }
        
        // 处理延迟详情
        RList<String> latencyList = redisson.getList("message:latency", StringCodec.INSTANCE);
        latencyList.clear();
        for (int i = 0; i < 50; i++) {
            latencyList.add("处理延迟: " + (3000 + random.nextInt(10000)) + "ms, 时间: " + System.currentTimeMillis());
        }
    }

    /**
     * 生成用户满意度监控数据
     */
    public void generateUserSatisfactionData(int days) {
        log.info("生成用户满意度监控数据");
        
        for (int i = 0; i < days; i++) {
            String date = LocalDate.now().minusDays(i).format(DATE_FORMATTER);
            
            // 满意度调查发送数据
            setCounterValue("satisfaction:survey:sent:" + CORP_ID, date, 200 + random.nextInt(100));
            
            // 满意度反馈数据
            setCounterValue("satisfaction:positive:" + CORP_ID, date, 120 + random.nextInt(60));
            setCounterValue("satisfaction:negative:" + CORP_ID, date, 30 + random.nextInt(20));
            
            // 反馈回收数据
            setCounterValue("feedback:recovery:sent:" + CORP_ID, date, 50 + random.nextInt(30));
        }
    }

    /**
     * 生成外部服务监控数据
     */
    public void generateExternalServiceData(int days) {
        log.info("生成外部服务监控数据");
        
        for (int i = 0; i < days; i++) {
            String date = LocalDate.now().minusDays(i).format(DATE_FORMATTER);
            
            // Dify API调用成功/失败数据
            setCounterValue("dify:api:success:" + CORP_ID, date, 500 + random.nextInt(200));
            setCounterValue("dify:api:failure:" + CORP_ID, date, 15 + random.nextInt(25));
            
            // 订单查询成功/失败数据
            setCounterValue("order:query:success:" + CORP_ID, date, 300 + random.nextInt(150));
            setCounterValue("order:query:failure:" + CORP_ID, date, 5 + random.nextInt(15));
        }
    }

    /**
     * 设置计数器值
     */
    private void setCounterValue(String key, String date, long value) {
        try {
            String fullKey = key + ":" + date;
            RAtomicLong atomicLong = redisson.getAtomicLong(fullKey);
            atomicLong.set(value);
            log.debug("设置计数器: {} = {}", fullKey, value);
        } catch (Exception e) {
            log.error("设置计数器失败: key={}, date={}, value={}", key, date, value, e);
        }
    }

    /**
     * 清空所有监控数据
     */
    public void clearAllMonitorData() {
        log.info("清空所有监控数据");
        
        // 这里可以添加清空逻辑，但要小心不要误删其他数据
        // 建议只在测试环境使用
        
        log.info("监控数据清空完成");
    }
}

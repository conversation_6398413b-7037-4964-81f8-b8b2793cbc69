<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控数据生成器</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .generator-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .btn-generate {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .btn-clear {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-clear:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .loading {
            display: none;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="generator-card p-5">
                    <!-- 标题区域 -->
                    <div class="text-center mb-5">
                        <h1 class="display-4 fw-bold text-primary mb-3">
                            <i class="fas fa-database me-3"></i>
                            监控数据生成器
                        </h1>
                        <p class="lead text-muted">为监控系统生成测试数据</p>
                    </div>

                    <!-- 操作区域 -->
                    <div class="row g-4 mb-5">
                        <!-- 生成所有数据 -->
                        <div class="col-md-6">
                            <div class="card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-magic feature-icon text-success"></i>
                                    <h5 class="card-title">生成所有监控数据</h5>
                                    <p class="card-text text-muted">一键生成所有类型的监控数据</p>
                                    <div class="mb-3">
                                        <label for="allDays" class="form-label">天数:</label>
                                        <select id="allDays" class="form-select">
                                            <option value="3">3天</option>
                                            <option value="7" selected>7天</option>
                                            <option value="15">15天</option>
                                            <option value="30">30天</option>
                                        </select>
                                    </div>
                                    <button type="button" class="btn btn-generate" onclick="generateAllData()">
                                        <i class="fas fa-play me-2"></i>
                                        生成所有数据
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 清空数据 -->
                        <div class="col-md-6">
                            <div class="card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-trash-alt feature-icon text-danger"></i>
                                    <h5 class="card-title">清空监控数据</h5>
                                    <p class="card-text text-muted">清空所有监控数据（谨慎操作）</p>
                                    <div class="mb-3">
                                        <small class="text-danger">此操作不可恢复，请谨慎使用</small>
                                    </div>
                                    <button type="button" class="btn btn-clear" onclick="clearAllData()">
                                        <i class="fas fa-trash me-2"></i>
                                        清空所有数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类生成区域 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h4 class="text-center mb-4">分类生成数据</h4>
                        </div>
                        
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="generateSpecificData('welcome')">
                                <i class="fas fa-comments me-2"></i>
                                欢迎语数据
                            </button>
                        </div>
                        
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-info w-100" onclick="generateSpecificData('ai-chat')">
                                <i class="fas fa-robot me-2"></i>
                                AI聊天数据
                            </button>
                        </div>
                        
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-warning w-100" onclick="generateSpecificData('message-process')">
                                <i class="fas fa-envelope me-2"></i>
                                消息处理数据
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-success w-100" onclick="generateSpecificData('user-satisfaction')">
                                <i class="fas fa-smile me-2"></i>
                                用户满意度数据
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="generateSpecificData('external-service')">
                                <i class="fas fa-external-link-alt me-2"></i>
                                外部服务数据
                            </button>
                        </div>
                    </div>

                    <!-- 加载状态 -->
                    <div class="loading text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">正在生成数据，请稍候...</p>
                    </div>

                    <!-- 结果显示区域 -->
                    <div id="result"></div>

                    <!-- 测试区域 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h4 class="text-center mb-4">测试功能</h4>
                        </div>

                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-info w-100" onclick="testRedis()">
                                <i class="fas fa-database me-2"></i>
                                测试Redis连接
                            </button>
                        </div>

                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-success w-100" onclick="generateSimpleData()">
                                <i class="fas fa-vial me-2"></i>
                                生成简单测试数据
                            </button>
                        </div>
                    </div>

                    <!-- 快速导航 -->
                    <div class="text-center mt-5">
                        <a href="/monitor/view/" class="btn btn-outline-primary me-3">
                            <i class="fas fa-arrow-left me-2"></i>
                            返回监控首页
                        </a>
                        <a href="/monitor/view/overview?corpId=ww5cfa32107e9a1f20&days=3" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i>
                            查看监控数据
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 生成所有数据
        function generateAllData() {
            const days = document.getElementById('allDays').value;
            showLoading();
            
            fetch('/monitor/data/generate?days=' + days, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.data);
                } else {
                    showAlert('danger', '生成失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '请求失败: ' + error.message);
            });
        }

        // 生成特定类型数据
        function generateSpecificData(type) {
            showLoading();
            
            fetch('/monitor/data/generate/' + type + '?days=7', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.data);
                } else {
                    showAlert('danger', '生成失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '请求失败: ' + error.message);
            });
        }

        // 清空所有数据
        function clearAllData() {
            if (!confirm('确定要清空所有监控数据吗？此操作不可恢复！')) {
                return;
            }
            
            showLoading();
            
            fetch('/monitor/data/clear', {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.data);
                } else {
                    showAlert('danger', '清空失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '请求失败: ' + error.message);
            });
        }

        // 显示加载状态
        function showLoading() {
            document.querySelector('.loading').style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.querySelector('.loading').style.display = 'none';
        }

        // 测试Redis连接
        function testRedis() {
            showLoading();

            fetch('/monitor/data/test-redis', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.data);
                } else {
                    showAlert('danger', '测试失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '请求失败: ' + error.message);
            });
        }

        // 生成简单测试数据
        function generateSimpleData() {
            showLoading();

            fetch('/monitor/data/generate-simple', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.data);
                } else {
                    showAlert('danger', '生成失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '请求失败: ' + error.message);
            });
        }

        // 显示结果
        function showAlert(type, message) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
    </script>
</body>
</html>
